"use client";

import { useState } from "react";
import WalletSearchInput from "@/components/ui/wallet-search-input";
import { GradientButton } from "@/components/ui/gradient-button";
import {
  getTokenInfo,
  formatTokenAmount,
  TokenInfo,
} from "@/lib/token-mapping";
import TokenFlowChart from "@/components/ui/token-flow-chart";

interface Transaction {
  blockNumber: string;
  timeStamp: string;
  hash: string;
  from: string;
  to: string;
  value: string;
  contractAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
  tokenDecimal?: string;
  isError: string;
}

interface TokenFlow {
  token: TokenInfo;
  incoming: number;
  outgoing: number;
  net: number;
  transactionCount: number;
}

export default function TokenFlowAnalyzer() {
  const [walletAddress, setWalletAddress] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [tokenFlows, setTokenFlows] = useState<TokenFlow[]>([]);
  const [error, setError] = useState("");

  const handleInputChange = (address: string) => {
    setWalletAddress(address);
    setError("");
  };

  const isValidEthereumAddress = (address: string): boolean => {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  };

  const fetchTransactions = async (address: string): Promise<Transaction[]> => {
    const apiKey = process.env.NEXT_PUBLIC_ETHERSCAN_API_KEY;
    if (!apiKey) {
      throw new Error("Etherscan API key not configured");
    }

    const response = await fetch(
      `https://api.etherscan.io/api?module=account&action=txlist&address=${address}&startblock=0&endblock=********&page=1&offset=1000&sort=desc&apikey=${apiKey}`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch transactions");
    }

    const data = await response.json();
    if (data.status !== "1") {
      console.log("API Response:", data);
      if (data.message && data.message.includes("rate limit")) {
        throw new Error(
          "Rate limit exceeded. Please wait a moment and try again. Consider upgrading your Etherscan API plan for higher limits."
        );
      }
      throw new Error(data.message || "Failed to fetch transactions");
    }

    console.log("Normal transactions found:", data.result.length);
    return data.result;
  };

  const fetchTokenTransactions = async (
    address: string
  ): Promise<Transaction[]> => {
    const apiKey = process.env.NEXT_PUBLIC_ETHERSCAN_API_KEY;
    if (!apiKey) {
      throw new Error("Etherscan API key not configured");
    }

    const response = await fetch(
      `https://api.etherscan.io/api?module=account&action=tokentx&address=${address}&startblock=0&endblock=********&page=1&offset=1000&sort=desc&apikey=${apiKey}`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch token transactions");
    }

    const data = await response.json();
    if (data.status !== "1") {
      console.log("Token API Response:", data);
      if (data.message && data.message.includes("rate limit")) {
        throw new Error(
          "Rate limit exceeded. Please wait a moment and try again. Consider upgrading your Etherscan API plan for higher limits."
        );
      }
      throw new Error(data.message || "Failed to fetch token transactions");
    }

    console.log("Token transactions found:", data.result.length);
    return data.result;
  };

  // Helper function to add delay between API calls
  const delay = (ms: number) =>
    new Promise((resolve) => setTimeout(resolve, ms));

  const analyzeTokenFlows = async (address: string): Promise<TokenFlow[]> => {
    // Fetch transactions sequentially with delay to respect rate limits (2 calls/sec)
    console.log("Fetching normal transactions...");
    const normalTxs = await fetchTransactions(address);

    console.log("Waiting 600ms before next API call...");
    await delay(600); // Wait 600ms to ensure we don't exceed 2 calls/sec

    console.log("Fetching token transactions...");
    const tokenTxs = await fetchTokenTransactions(address);

    console.log("Analyzing token flows for address:", address);
    console.log("Normal transactions:", normalTxs.length);
    console.log("Token transactions:", tokenTxs.length);

    const tokenBalances: Record<
      string,
      {
        incoming: number;
        outgoing: number;
        transactions: number;
        tokenInfo: TokenInfo;
      }
    > = {};

    // Process normal ETH transactions
    const ethToken = getTokenInfo("******************************************");
    if (ethToken) {
      tokenBalances[ethToken.address] = {
        incoming: 0,
        outgoing: 0,
        transactions: 0,
        tokenInfo: ethToken,
      };

      normalTxs.forEach((tx) => {
        if (tx.isError === "0") {
          const value = formatTokenAmount(tx.value, 18);

          if (tx.to.toLowerCase() === address.toLowerCase()) {
            // Receiving ETH
            tokenBalances[ethToken.address].incoming += value;
          } else if (tx.from.toLowerCase() === address.toLowerCase()) {
            // Sending ETH
            tokenBalances[ethToken.address].outgoing += value;
          }
          tokenBalances[ethToken.address].transactions++;
        }
      });
    }

    // Process token transactions
    tokenTxs.forEach((tx) => {
      if (tx.isError === "0" && tx.contractAddress && tx.tokenSymbol) {
        // Try to get token info from our database, or create from transaction data
        let tokenInfo = getTokenInfo(tx.contractAddress);

        if (!tokenInfo) {
          // Create token info from transaction data
          tokenInfo = {
            address: tx.contractAddress,
            symbol: tx.tokenSymbol,
            name: tx.tokenName || tx.tokenSymbol,
            decimals: parseInt(tx.tokenDecimal || "18"),
            logoURI: undefined,
          };
        }

        const tokenKey = tokenInfo.address.toLowerCase();
        if (!tokenBalances[tokenKey]) {
          tokenBalances[tokenKey] = {
            incoming: 0,
            outgoing: 0,
            transactions: 0,
            tokenInfo,
          };
        }

        const decimals = parseInt(tx.tokenDecimal || "18");
        const value = formatTokenAmount(tx.value, decimals);

        if (tx.to.toLowerCase() === address.toLowerCase()) {
          // Receiving tokens
          tokenBalances[tokenKey].incoming += value;
        } else if (tx.from.toLowerCase() === address.toLowerCase()) {
          // Sending tokens
          tokenBalances[tokenKey].outgoing += value;
        }
        tokenBalances[tokenKey].transactions++;

        console.log(
          `Processed ${tokenInfo.symbol}: ${value} (${
            tx.to.toLowerCase() === address.toLowerCase()
              ? "incoming"
              : "outgoing"
          })`
        );
      }
    });

    // Convert to TokenFlow array
    const flows: TokenFlow[] = [];
    Object.values(tokenBalances).forEach((balance) => {
      if (balance.incoming > 0 || balance.outgoing > 0) {
        flows.push({
          token: balance.tokenInfo,
          incoming: balance.incoming,
          outgoing: balance.outgoing,
          net: balance.incoming - balance.outgoing,
          transactionCount: balance.transactions,
        });
      }
    });

    console.log("Token flows found:", flows.length);
    console.log(
      "Token flows details:",
      flows.map(
        (f) =>
          `${f.token.symbol}: +${f.incoming.toFixed(4)} -${f.outgoing.toFixed(
            4
          )} = ${f.net.toFixed(4)}`
      )
    );
    return flows.sort((a, b) => Math.abs(b.net) - Math.abs(a.net));
  };

  const handleSubmit = async (address: string) => {
    if (!address) {
      setError("Please enter a wallet address");
      return;
    }

    if (!isValidEthereumAddress(address)) {
      setError("Please enter a valid Ethereum address");
      return;
    }

    setIsLoading(true);
    setError("");
    setTokenFlows([]);

    try {
      const flows = await analyzeTokenFlows(address);
      setTokenFlows(flows);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while analyzing protocol interactions"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Token Flow Analyzer
            </h1>

            <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4 max-w-2xl mx-auto">
              <p className="text-blue-300 text-sm">
                ℹ️ <strong>Info:</strong> We analyze your last 200 transactions
                to identify DeFi protocol interactions. Results are based on
                known protocol addresses and may not capture all interactions.
              </p>
            </div>
          </div>

          {/* Input Form */}
          <div className="mb-12 flex items-center gap-2 mx-auto justify-center">
            <WalletSearchInput
              placeholder="Enter your Ethereum wallet address..."
              onChange={handleInputChange}
              onSubmit={handleSubmit}
            />

            <GradientButton
              onClick={() => handleSubmit(walletAddress)}
              disabled={isLoading || !walletAddress}
              className="min-w-[200px] text-white font-bold"
            >
              {isLoading ? "Analyzing..." : "Analyze Token Flows"}
            </GradientButton>

            {error && (
              <div className="mt-4 text-center">
                <p className="text-red-400 bg-red-900/20 border border-red-600/30 rounded-lg p-3 inline-block">
                  {error}
                </p>
              </div>
            )}
          </div>

          {/* Results */}
          {tokenFlows.length > 0 && (
            <div className="space-y-8">
              <h2 className="text-3xl font-bold text-center mb-8">
                Your Token Flows
              </h2>

              {/* Token Flow Chart */}
              <div className="mb-8">
                <TokenFlowChart data={tokenFlows} width={800} height={600} />
              </div>

              {/* Token List */}
              <div className="grid gap-4 max-w-4xl mx-auto">
                <h3 className="text-2xl font-bold text-center mb-6">
                  Token Details
                </h3>
                {tokenFlows.map((flow) => (
                  <div
                    key={flow.token.symbol}
                    className="bg-gray-900/50 border border-gray-700 rounded-xl p-6 hover:border-green-500/50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {flow.token.logoURI && (
                          <img
                            src={flow.token.logoURI}
                            alt={flow.token.symbol}
                            className="w-12 h-12 rounded-full"
                            onError={(e) => {
                              e.currentTarget.style.display = "none";
                            }}
                          />
                        )}
                        <div>
                          <h4 className="text-xl font-semibold text-white">
                            {flow.token.symbol}
                          </h4>
                          <p className="text-gray-400">{flow.token.name}</p>
                        </div>
                      </div>

                      <div className="text-right space-y-1">
                        <div className="flex items-center space-x-4">
                          <div className="text-center">
                            <p className="text-lg font-bold text-green-400">
                              +{flow.incoming.toFixed(4)}
                            </p>
                            <p className="text-xs text-gray-400">Incoming</p>
                          </div>
                          <div className="text-center">
                            <p className="text-lg font-bold text-red-400">
                              -{flow.outgoing.toFixed(4)}
                            </p>
                            <p className="text-xs text-gray-400">Outgoing</p>
                          </div>
                          <div className="text-center">
                            <p
                              className={`text-lg font-bold ${
                                flow.net > 0 ? "text-green-400" : "text-red-400"
                              }`}
                            >
                              {flow.net > 0 ? "+" : ""}
                              {flow.net.toFixed(4)}
                            </p>
                            <p className="text-xs text-gray-400">Net</p>
                          </div>
                        </div>
                        <p className="text-sm text-gray-400">
                          {flow.transactionCount} transactions
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {tokenFlows.length === 0 && !isLoading && (
            <div className="text-center py-12">
              <p className="text-yellow-400 text-xl">
                🔍 No token flows found in your recent transactions.
              </p>
              <p className="text-gray-400 mt-2">
                Try with a different address or check if you have any token
                transactions.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
