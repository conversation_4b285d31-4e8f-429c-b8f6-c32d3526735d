"use client";

import { useState } from "react";
import WalletSearchInput from "@/components/ui/wallet-search-input";
import { GradientButton } from "@/components/ui/gradient-button";
import ProtocolBubbleChart, {
  ProtocolInteraction,
} from "@/components/ui/protocol-bubble-chart";
import ProtocolBarChart from "@/components/ui/protocol-bar-chart";
import ProtocolDoughnutChart from "@/components/ui/protocol-doughnut-chart";
import { getProtocolByAddress } from "@/lib/defi-protocols";

interface Transaction {
  blockNumber: string;
  timeStamp: string;
  hash: string;
  from: string;
  to: string;
  value: string;
  contractAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
  tokenDecimal?: string;
  isError: string;
}

export default function DeFiProtocolAnalyzer() {
  const [walletAddress, setWalletAddress] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [protocolInteractions, setProtocolInteractions] = useState<
    ProtocolInteraction[]
  >([]);
  const [allTransactions, setAllTransactions] = useState<Transaction[]>([]);
  const [error, setError] = useState("");

  const handleInputChange = (address: string) => {
    setWalletAddress(address);
    setError("");
  };

  const isValidEthereumAddress = (address: string): boolean => {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  };

  const fetchTransactions = async (address: string): Promise<Transaction[]> => {
    const apiKey = process.env.NEXT_PUBLIC_ETHERSCAN_API_KEY;
    if (!apiKey) {
      throw new Error("Etherscan API key not configured");
    }

    const response = await fetch(
      `https://api.etherscan.io/api?module=account&action=txlist&address=${address}&startblock=0&endblock=********&page=1&offset=1000&sort=desc&apikey=${apiKey}`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch transactions");
    }

    const data = await response.json();
    if (data.status !== "1") {
      console.log("API Response:", data);
      throw new Error(data.message || "Failed to fetch transactions");
    }

    console.log("Normal transactions found:", data.result.length);
    return data.result;
  };

  const fetchTokenTransactions = async (
    address: string
  ): Promise<Transaction[]> => {
    const apiKey = process.env.NEXT_PUBLIC_ETHERSCAN_API_KEY;
    if (!apiKey) {
      throw new Error("Etherscan API key not configured");
    }

    const response = await fetch(
      `https://api.etherscan.io/api?module=account&action=tokentx&address=${address}&startblock=0&endblock=********&page=1&offset=1000&sort=desc&apikey=${apiKey}`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch token transactions");
    }

    const data = await response.json();
    if (data.status !== "1") {
      console.log("Token API Response:", data);
      throw new Error(data.message || "Failed to fetch token transactions");
    }

    console.log("Token transactions found:", data.result.length);
    return data.result;
  };

  const analyzeProtocolInteractions = async (
    address: string
  ): Promise<ProtocolInteraction[]> => {
    const [normalTxs, tokenTxs] = await Promise.all([
      fetchTransactions(address),
      fetchTokenTransactions(address),
    ]);

    console.log("Analyzing transactions for address:", address);
    console.log("Normal transactions:", normalTxs.length);
    console.log("Token transactions:", tokenTxs.length);

    const protocolCounts: Record<
      string,
      {
        protocol: string;
        category: string;
        color: string;
        description?: string;
        transactionCount: number;
      }
    > = {};

    // Analyze normal transactions
    normalTxs.forEach((tx) => {
      if (tx.isError === "0") {
        const counterpartyAddress =
          tx.from.toLowerCase() === address.toLowerCase() ? tx.to : tx.from;
        const protocol = getProtocolByAddress(counterpartyAddress);

        if (protocol) {
          console.log(
            `Found ${protocol.protocol} interaction:`,
            counterpartyAddress
          );
          const key = protocol.protocol;
          if (!protocolCounts[key]) {
            protocolCounts[key] = {
              protocol: protocol.protocol,
              category: protocol.category,
              color: protocol.color,
              description: protocol.description,
              transactionCount: 0,
            };
          }
          protocolCounts[key].transactionCount++;
        }
      }
    });

    // Analyze token transactions
    tokenTxs.forEach((tx) => {
      if (tx.isError === "0") {
        const counterpartyAddress =
          tx.from.toLowerCase() === address.toLowerCase() ? tx.to : tx.from;
        const protocol = getProtocolByAddress(counterpartyAddress);

        if (protocol) {
          const key = protocol.protocol;
          if (!protocolCounts[key]) {
            protocolCounts[key] = {
              protocol: protocol.protocol,
              category: protocol.category,
              color: protocol.color,
              description: protocol.description,
              transactionCount: 0,
            };
          }
          protocolCounts[key].transactionCount++;
        }
      }
    });

    // Convert to array and sort by transaction count
    const interactions: ProtocolInteraction[] = Object.values(
      protocolCounts
    ).sort((a, b) => b.transactionCount - a.transactionCount);

    console.log("Unique protocols found:", Object.keys(protocolCounts).length);
    console.log("Final interactions:", interactions);
    return interactions;
  };

  const handleSubmit = async (address: string) => {
    if (!address) {
      setError("Please enter a wallet address");
      return;
    }

    if (!isValidEthereumAddress(address)) {
      setError("Please enter a valid Ethereum address");
      return;
    }

    setIsLoading(true);
    setError("");
    setProtocolInteractions([]);
    setAllTransactions([]);

    try {
      const [normalTxs, tokenTxs] = await Promise.all([
        fetchTransactions(address),
        fetchTokenTransactions(address),
      ]);

      const allTxs = [...normalTxs, ...tokenTxs];
      setAllTransactions(allTxs);

      const interactions = await analyzeProtocolInteractions(address);
      setProtocolInteractions(interactions);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while analyzing protocol interactions"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              DeFi Protocol Analyzer
            </h1>
            <p className="text-xl text-gray-300 mb-4 max-w-2xl mx-auto">
              Discover which DeFi protocols you've interacted with and visualize
              your on-chain activity.
            </p>
            <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4 max-w-2xl mx-auto">
              <p className="text-blue-300 text-sm">
                ℹ️ <strong>Info:</strong> We analyze your last 200 transactions
                to identify DeFi protocol interactions. Results are based on
                known protocol addresses and may not capture all interactions.
              </p>
            </div>
          </div>

          {/* Input Form */}
          <div className="mb-12">
            <WalletSearchInput
              placeholder="Enter your Ethereum wallet address..."
              onChange={handleInputChange}
              onSubmit={handleSubmit}
            />

            <div className="flex justify-center mt-6">
              <GradientButton
                onClick={() => handleSubmit(walletAddress)}
                disabled={isLoading || !walletAddress}
                className="min-w-[200px] text-white font-bold"
              >
                {isLoading ? "Analyzing..." : "Analyze Protocols"}
              </GradientButton>
            </div>

            {error && (
              <div className="mt-4 text-center">
                <p className="text-red-400 bg-red-900/20 border border-red-600/30 rounded-lg p-3 inline-block">
                  {error}
                </p>
              </div>
            )}
          </div>

          {/* Results */}
          {protocolInteractions.length > 0 && (
            <div className="space-y-8">
              <h2 className="text-3xl font-bold text-center mb-8">
                Your DeFi Protocol Interactions
              </h2>

              {/* Charts Section */}
              <div className="space-y-8 mb-8">
                {/* Bar Chart - Main visualization */}
                <div>
                  <h3 className="text-2xl font-bold text-center mb-4">
                    Transaction Volume by Protocol
                  </h3>
                  <ProtocolBarChart
                    data={protocolInteractions}
                    width={800}
                    height={400}
                  />
                </div>

                {/* Two charts side by side */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Bubble Chart */}
                  <div>
                    <h3 className="text-xl font-bold text-center mb-4">
                      Protocol Bubble View
                    </h3>
                    <ProtocolBubbleChart
                      data={protocolInteractions}
                      width={400}
                      height={400}
                    />
                  </div>

                  {/* Doughnut Chart */}
                  <div>
                    <h3 className="text-xl font-bold text-center mb-4">
                      Category Distribution
                    </h3>
                    <ProtocolDoughnutChart
                      data={protocolInteractions}
                      width={400}
                      height={400}
                    />
                  </div>
                </div>
              </div>

              {/* Protocol List */}
              <div className="grid gap-4 max-w-4xl mx-auto">
                <h3 className="text-2xl font-bold text-center mb-6">
                  Protocol Details
                </h3>
                {protocolInteractions.map((interaction, index) => (
                  <div
                    key={interaction.protocol}
                    className="bg-gray-900/50 border border-gray-700 rounded-xl p-6 hover:border-purple-500/50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold"
                          style={{ backgroundColor: interaction.color }}
                        >
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="text-xl font-semibold text-white">
                            {interaction.protocol}
                          </h4>
                          <p className="text-gray-400">
                            {interaction.category}
                          </p>
                          {interaction.description && (
                            <p className="text-gray-500 text-sm">
                              {interaction.description}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="text-right">
                        <p className="text-2xl font-bold text-blue-400">
                          {interaction.transactionCount}
                        </p>
                        <p className="text-sm text-gray-400">transactions</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {protocolInteractions.length === 0 &&
                allTransactions.length > 0 && (
                  <div className="text-center py-12">
                    <p className="text-yellow-400 text-xl mb-4">
                      🔍 No DeFi protocol interactions found in your recent
                      transactions.
                    </p>
                    <p className="text-gray-400 mb-6">
                      Found {allTransactions.length} total transactions, but
                      none with known DeFi protocols.
                    </p>

                    <div className="max-w-4xl mx-auto">
                      <h3 className="text-xl font-bold mb-4">
                        Recent Transaction Addresses
                      </h3>
                      <div className="bg-gray-900/50 rounded-lg p-4 max-h-96 overflow-y-auto">
                        {allTransactions.slice(0, 20).map((tx, index) => {
                          const counterparty =
                            tx.from.toLowerCase() ===
                            walletAddress.toLowerCase()
                              ? tx.to
                              : tx.from;
                          return (
                            <div
                              key={tx.hash}
                              className="flex justify-between items-center py-2 border-b border-gray-700 last:border-b-0"
                            >
                              <div className="flex items-center space-x-2">
                                <span className="text-gray-400 text-sm">
                                  #{index + 1}
                                </span>
                                <span className="text-white font-mono text-sm">
                                  {counterparty}
                                </span>
                              </div>
                              <div className="text-gray-400 text-xs">
                                {tx.tokenSymbol || "ETH"}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      <p className="text-gray-500 text-sm mt-2">
                        Showing first 20 transactions. These addresses are not
                        in our DeFi protocol database.
                      </p>
                    </div>
                  </div>
                )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
