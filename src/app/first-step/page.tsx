"use client";

import { useState } from "react";
import WalletSearchInput from "@/components/ui/wallet-search-input";
import { GradientButton } from "@/components/ui/gradient-button";
import {
  getTokenInfo,
  formatTokenAmount,
  TokenInfo,
} from "@/lib/token-mapping";

interface Transaction {
  blockNumber: string;
  timeStamp: string;
  hash: string;
  from: string;
  to: string;
  value: string;
  contractAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
  tokenDecimal?: string;
  isError: string;
}

interface TokenLoss {
  token: TokenInfo;
  totalDeposited: number;
  totalWithdrawn: number;
  netLoss: number;
  transactionCount: number;
}

export default function CryptoLossCalculator() {
  const [walletAddress, setWalletAddress] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<TokenLoss[]>([]);
  const [error, setError] = useState("");

  const handleInputChange = (address: string) => {
    setWalletAddress(address);
    setError("");
  };

  const isValidEthereumAddress = (address: string): boolean => {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  };

  const fetchTransactions = async (address: string): Promise<Transaction[]> => {
    const apiKey = process.env.NEXT_PUBLIC_ETHERSCAN_API_KEY;
    if (!apiKey) {
      throw new Error("Etherscan API key not configured");
    }

    const response = await fetch(
      `https://api.etherscan.io/api?module=account&action=txlist&address=${address}&startblock=0&endblock=********&page=1&offset=1000&sort=desc&apikey=${apiKey}`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch transactions");
    }

    const data = await response.json();
    if (data.status !== "1") {
      throw new Error(data.message || "Failed to fetch transactions");
    }

    return data.result;
  };

  const fetchTokenTransactions = async (
    address: string
  ): Promise<Transaction[]> => {
    const apiKey = process.env.NEXT_PUBLIC_ETHERSCAN_API_KEY;
    if (!apiKey) {
      throw new Error("Etherscan API key not configured");
    }

    const response = await fetch(
      `https://api.etherscan.io/api?module=account&action=tokentx&address=${address}&startblock=0&endblock=********&page=1&offset=1000&sort=desc&apikey=${apiKey}`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch token transactions");
    }

    const data = await response.json();
    if (data.status !== "1") {
      throw new Error(data.message || "Failed to fetch token transactions");
    }

    return data.result;
  };

  const calculateLosses = async (address: string): Promise<TokenLoss[]> => {
    const [normalTxs, tokenTxs] = await Promise.all([
      fetchTransactions(address),
      fetchTokenTransactions(address),
    ]);

    const tokenBalances: Record<
      string,
      {
        deposited: number;
        withdrawn: number;
        transactions: number;
        tokenInfo: TokenInfo;
      }
    > = {};

    // Process normal ETH transactions
    const ethToken = getTokenInfo("******************************************");
    if (ethToken) {
      tokenBalances[ethToken.address] = {
        deposited: 0,
        withdrawn: 0,
        transactions: 0,
        tokenInfo: ethToken,
      };

      normalTxs.forEach((tx) => {
        if (tx.isError === "0") {
          const value = formatTokenAmount(tx.value, 18);

          if (tx.to.toLowerCase() === address.toLowerCase()) {
            // Receiving ETH
            tokenBalances[ethToken.address].deposited += value;
          } else if (tx.from.toLowerCase() === address.toLowerCase()) {
            // Sending ETH
            tokenBalances[ethToken.address].withdrawn += value;
          }
          tokenBalances[ethToken.address].transactions++;
        }
      });
    }

    // Process token transactions
    tokenTxs.forEach((tx) => {
      if (tx.isError === "0" && tx.contractAddress) {
        const tokenInfo = getTokenInfo(tx.contractAddress);
        if (tokenInfo) {
          if (!tokenBalances[tokenInfo.address]) {
            tokenBalances[tokenInfo.address] = {
              deposited: 0,
              withdrawn: 0,
              transactions: 0,
              tokenInfo,
            };
          }

          const decimals = parseInt(tx.tokenDecimal || "18");
          const value = formatTokenAmount(tx.value, decimals);

          if (tx.to.toLowerCase() === address.toLowerCase()) {
            // Receiving tokens
            tokenBalances[tokenInfo.address].deposited += value;
          } else if (tx.from.toLowerCase() === address.toLowerCase()) {
            // Sending tokens
            tokenBalances[tokenInfo.address].withdrawn += value;
          }
          tokenBalances[tokenInfo.address].transactions++;
        }
      }
    });

    // Calculate losses
    const losses: TokenLoss[] = [];
    Object.values(tokenBalances).forEach((balance) => {
      const netLoss = balance.withdrawn - balance.deposited;
      if (netLoss > 0) {
        losses.push({
          token: balance.tokenInfo,
          totalDeposited: balance.deposited,
          totalWithdrawn: balance.withdrawn,
          netLoss,
          transactionCount: balance.transactions,
        });
      }
    });

    return losses.sort((a, b) => b.netLoss - a.netLoss);
  };

  const handleSubmit = async (address: string) => {
    if (!address) {
      setError("Please enter a wallet address");
      return;
    }

    if (!isValidEthereumAddress(address)) {
      setError("Please enter a valid Ethereum address");
      return;
    }

    setIsLoading(true);
    setError("");
    setResults([]);

    try {
      const losses = await calculateLosses(address);
      setResults(losses);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while calculating losses"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">
              Crypto Loss Calculator
            </h1>
            <p className="text-xl text-gray-300 mb-4 max-w-2xl mx-auto">
              Analyze your crypto trading losses with detailed insights and
              statistics.
            </p>
            <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4 max-w-2xl mx-auto">
              <p className="text-yellow-300 text-sm">
                ⚠️ <strong>Disclaimer:</strong> Results may not be 100% accurate
                as we use the free Etherscan API. This tool provides an estimate
                of your losses over recent months for educational purposes only.
              </p>
            </div>
          </div>

          {/* Input Form */}
          <div className="mb-12">
            <WalletSearchInput
              placeholder="Enter your Ethereum wallet address..."
              onChange={handleInputChange}
              onSubmit={handleSubmit}
            />

            <div className="flex justify-center mt-6">
              <GradientButton
                onClick={() => handleSubmit(walletAddress)}
                disabled={isLoading || !walletAddress}
                className="min-w-[200px]"
              >
                {isLoading ? "Calculating..." : "Calculate Losses"}
              </GradientButton>
            </div>

            {error && (
              <div className="mt-4 text-center">
                <p className="text-red-400 bg-red-900/20 border border-red-600/30 rounded-lg p-3 inline-block">
                  {error}
                </p>
              </div>
            )}
          </div>

          {/* Results */}
          {results.length > 0 && (
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-center mb-8">
                Your Crypto Losses
              </h2>

              <div className="grid gap-6">
                {results.map((loss) => (
                  <div
                    key={loss.token.address}
                    className="bg-gray-900/50 border border-gray-700 rounded-xl p-6 hover:border-red-500/50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {loss.token.logoURI && (
                          <img
                            src={loss.token.logoURI}
                            alt={loss.token.symbol}
                            className="w-12 h-12 rounded-full"
                            onError={(e) => {
                              e.currentTarget.style.display = "none";
                            }}
                          />
                        )}
                        <div>
                          <h3 className="text-xl font-semibold text-white">
                            {loss.token.name}
                          </h3>
                          <p className="text-gray-400">{loss.token.symbol}</p>
                        </div>
                      </div>

                      <div className="text-right">
                        <p className="text-2xl font-bold text-red-400">
                          -{loss.netLoss.toFixed(6)} {loss.token.symbol}
                        </p>
                        <p className="text-sm text-gray-400">
                          {loss.transactionCount} transactions
                        </p>
                      </div>
                    </div>

                    <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-400">Total Deposited</p>
                        <p className="text-green-400 font-semibold">
                          {loss.totalDeposited.toFixed(6)} {loss.token.symbol}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-400">Total Withdrawn</p>
                        <p className="text-red-400 font-semibold">
                          {loss.totalWithdrawn.toFixed(6)} {loss.token.symbol}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {results.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-green-400 text-xl">
                    🎉 Great news! No significant losses detected in your recent
                    transactions.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
