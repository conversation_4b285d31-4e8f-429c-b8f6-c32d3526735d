import Link from "next/link";
import { BentoGrid, BentoGridItem } from "../../components/ui/bento-grid";
import {
  IconArrowWaveRightUp,
  IconBoxAlignRightFilled,
  IconBoxAlignTopLeft,
  IconClipboardCopy,
  IconFileBroken,
  IconSignature,
  IconTableColumn,
} from "@tabler/icons-react";

const Skeleton = () => (
  <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-neutral-200 dark:from-neutral-900 dark:to-neutral-800 to-neutral-100"></div>
);

const SkeletonOne = () => {
  return (
    <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-red-900/20 via-red-800/20 to-orange-900/20 p-4">
      <div className="w-full bg-gradient-to-br from-red-500/20 to-orange-500/20 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-2xl font-bold mb-2">-$12,542</div>
          <div className="text-orange-300 text-sm">Total Losses</div>
        </div>
      </div>
    </div>
  );
};

const SkeletonTwo = () => {
  return (
    <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20 p-4">
      <div className="w-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="text-purple-400 text-lg font-bold mb-2">$8,234</div>
          <div className="text-blue-300 text-sm">Portfolio Value</div>
        </div>
      </div>
    </div>
  );
};

const SkeletonThree = () => {
  return (
    <div className="flex flex-1 w-full h-full min-h-[6rem] rounded-xl bg-gradient-to-br from-green-900/20 via-emerald-900/20 to-teal-900/20 p-4">
      <div className="w-full bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="text-green-400 text-lg font-bold mb-2">12.5% APY</div>
          <div className="text-emerald-300 text-sm">Best Yield</div>
        </div>
      </div>
    </div>
  );
};

export default function Home() {
  const items = [
    {
      title: "Crypto Loss Calculator",
      description:
        "Track and analyze your crypto trading losses with detailed insights and statistics.",
      header: <SkeletonOne />,
      icon: <IconClipboardCopy className="h-4 w-4 text-red-400" />,
      className: "md:col-span-2",
    },
    {
      title: "Portfolio Tracker",
      description: "Monitor your crypto portfolio performance in real-time.",
      header: <SkeletonTwo />,
      icon: <IconFileBroken className="h-4 w-4 text-purple-400" />,
    },
    {
      title: "DeFi Yield Calculator",
      description:
        "Calculate potential yields from various DeFi protocols and strategies.",
      header: <SkeletonThree />,
      icon: <IconSignature className="h-4 w-4 text-green-400" />,
    },
    {
      title: "NFT Collection Analyzer",
      description:
        "Analyze NFT collections and their market performance over time.",
      header: <Skeleton />,
      icon: <IconTableColumn className="h-4 w-4 text-blue-400" />,
    },
    {
      title: "Gas Fee Optimizer",
      description:
        "Find the best times to make transactions with optimal gas fees.",
      header: <Skeleton />,
      icon: <IconArrowWaveRightUp className="h-4 w-4 text-yellow-400" />,
    },
    {
      title: "Whale Tracker",
      description:
        "Track large crypto transactions and whale movements across blockchains.",
      header: <Skeleton />,
      icon: <IconBoxAlignTopLeft className="h-4 w-4 text-cyan-400" />,
    },
    {
      title: "Smart Contract Auditor",
      description: "Basic security checks and analysis for smart contracts.",
      header: <Skeleton />,
      icon: <IconBoxAlignRightFilled className="h-4 w-4 text-pink-400" />,
    },
  ];
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
            Crypto Mini Apps
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Discover new crypto applications daily. Each app is announced once
            per day through our Twitter feed.
          </p>
        </div>

        {/* Bento Grid */}
        <BentoGrid className="max-w-4xl mx-auto mb-16">
          {items.map((item, index) => {
            if (index === 0) {
              return (
                <Link key={item.title} href="/first-step">
                  <BentoGridItem
                    title={item.title}
                    description={item.description}
                    header={item.header}
                    icon={item.icon}
                    className={`${item.className} cursor-pointer hover:scale-105 transition-transform duration-300`}
                  />
                </Link>
              );
            }
            return (
              <BentoGridItem
                key={item.title}
                title={item.title}
                description={item.description}
                header={item.header}
                icon={item.icon}
                className={item.className}
              />
            );
          })}
        </BentoGrid>

        {/* Twitter Feed Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-white text-center mb-8">
            Latest Announcements
          </h2>
          <div className="bg-gray-800/50 rounded-xl p-8 backdrop-blur-sm border border-gray-700">
            <p className="text-gray-300 text-center">
              Follow our Twitter feed for daily app announcements and updates!
            </p>
            {/* Tweet carousel will be added here */}
          </div>
        </div>
      </div>
    </div>
  );
}
