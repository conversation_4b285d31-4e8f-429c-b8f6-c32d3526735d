import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
            Crypto Mini Apps
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Discover new crypto applications daily. Each app is announced once
            per day through our Twitter feed.
          </p>
        </div>

        {/* Animated Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <div
              key={item}
              className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-gray-800 to-gray-900 p-6 hover:scale-105 transition-all duration-300 border border-gray-700 hover:border-purple-500"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 to-blue-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-12 h-12 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-lg mb-4 flex items-center justify-center">
                  <span className="text-white font-bold text-xl">{item}</span>
                </div>
                <h3 className="text-white font-semibold text-lg mb-2">
                  Mini App {item}
                </h3>
                <p className="text-gray-400 text-sm">Coming soon...</p>
              </div>
            </div>
          ))}
        </div>

        {/* First App Link */}
        <div className="text-center">
          <Link
            href="/first-step"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            Try Our First App: Crypto Loss Calculator
            <svg
              className="ml-2 w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 7l5 5m0 0l-5 5m5-5H6"
              />
            </svg>
          </Link>
        </div>

        {/* Twitter Feed Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-white text-center mb-8">
            Latest Announcements
          </h2>
          <div className="bg-gray-800/50 rounded-xl p-8 backdrop-blur-sm border border-gray-700">
            <p className="text-gray-300 text-center">
              Follow our Twitter feed for daily app announcements and updates!
            </p>
            {/* Tweet carousel will be added here */}
          </div>
        </div>
      </div>
    </div>
  );
}
