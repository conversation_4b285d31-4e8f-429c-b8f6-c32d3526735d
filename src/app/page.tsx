"use client";

import { useEffect } from "react";
import ScrollExpandMedia from "@/components/ui/scroll-expand-media";
import { BentoCard, BentoGrid } from "@/components/ui/bento-grid";
import {
  CalendarIcon,
  FileTextIcon,
  GlobeIcon,
  InputIcon,
  BellIcon,
} from "@radix-ui/react-icons";

const cryptoApps = [
  {
    Icon: FileTextIcon,
    name: "DeFi Protocol Analyzer",
    description:
      "Discover which DeFi protocols you've interacted with and visualize your on-chain activity.",
    href: "/first-step",
    cta: "Try Now",
    background: (
      <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
    ),
    className: "lg:row-start-1 lg:row-end-4 lg:col-start-2 lg:col-end-3",
  },
  // {
  //   Icon: InputIcon,
  //   name: "<PERSON><PERSON><PERSON>eld Tracker",
  //   description: "Track your DeFi yields across multiple protocols and chains.",
  //   href: "/",
  //   cta: "Coming Soon",
  //   background: (
  //     <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-full blur-xl" />
  //   ),
  //   className: "lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3",
  // },
  // {
  //   Icon: GlobeIcon,
  //   name: "NFT Portfolio",
  //   description:
  //     "Manage and track your NFT collection across multiple marketplaces.",
  //   href: "/",
  //   cta: "Coming Soon",
  //   background: (
  //     <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-xl" />
  //   ),
  //   className: "lg:col-start-1 lg:col-end-2 lg:row-start-3 lg:row-end-4",
  // },
  // {
  //   Icon: CalendarIcon,
  //   name: "Crypto Calendar",
  //   description: "Stay updated with important crypto events and announcements.",
  //   href: "/",
  //   cta: "Coming Soon",
  //   background: (
  //     <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full blur-xl" />
  //   ),
  //   className: "lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-2",
  // },
  // {
  //   Icon: BellIcon,
  //   name: "Price Alerts",
  //   description:
  //     "Get notified when your favorite cryptocurrencies hit target prices.",
  //   href: "/",
  //   cta: "Coming Soon",
  //   background: (
  //     <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-full blur-xl" />
  //   ),
  //   className: "lg:col-start-3 lg:col-end-3 lg:row-start-2 lg:row-end-4",
  // },
];

const BentoGridSection = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 py-16">
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold mb-4 text-white">Crypto Mini Apps</h2>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Discover new crypto applications daily. Each app is announced once per
          day through our Twitter feed.
        </p>
      </div>

      <BentoGrid className="lg:grid-rows-3">
        {cryptoApps.map((feature) => (
          <BentoCard key={feature.name} {...feature} />
        ))}
      </BentoGrid>
    </div>
  );
};

export default function Home() {
  useEffect(() => {
    window.scrollTo(0, 0);
    const resetEvent = new Event("resetSection");
    window.dispatchEvent(resetEvent);
  }, []);
  return (
    <div className="min-h-screen bg-black">
      <ScrollExpandMedia
        mediaType="image"
        mediaSrc="/eth.jpg"
        posterSrc="https://images.pexels.com/videos/5752729/space-earth-universe-cosmos-5752729.jpeg"
        bgImageSrc="/main-bg.jpg"
        title="Crypto Mini Apps"
        scrollToExpand="Scroll to Explore Apps"
        textBlend
      >
        <BentoGridSection />
      </ScrollExpandMedia>
    </div>
  );
}
