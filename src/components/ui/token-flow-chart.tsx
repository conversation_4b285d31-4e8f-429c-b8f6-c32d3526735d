'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface TokenFlow {
  token: {
    symbol: string;
    name: string;
    logoURI?: string;
  };
  incoming: number;
  outgoing: number;
  net: number;
  transactionCount: number;
}

interface TokenFlowChartProps {
  data: TokenFlow[];
  width?: number;
  height?: number;
}

const TokenFlowChart: React.FC<TokenFlowChartProps> = ({
  data,
  width = 800,
  height = 600,
}) => {
  if (!data.length) {
    return (
      <div className="w-full flex justify-center items-center h-96 border border-gray-700 rounded-lg bg-gray-900/50">
        <p className="text-gray-400">No token flows to display</p>
      </div>
    );
  }

  // Sort data by absolute net value for better visualization
  const sortedData = [...data].sort((a, b) => Math.abs(b.net) - Math.abs(a.net));

  const chartData = {
    labels: sortedData.map(flow => flow.token.symbol),
    datasets: [
      {
        label: 'Incoming',
        data: sortedData.map(flow => flow.incoming),
        backgroundColor: 'rgba(34, 197, 94, 0.8)', // Green
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 2,
        borderRadius: 4,
      },
      {
        label: 'Outgoing',
        data: sortedData.map(flow => -flow.outgoing), // Negative for downward bars
        backgroundColor: 'rgba(239, 68, 68, 0.8)', // Red
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 2,
        borderRadius: 4,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: '#ffffff',
          usePointStyle: true,
          pointStyle: 'rect',
        },
      },
      title: {
        display: true,
        text: 'Token Flows: Incoming vs Outgoing',
        color: '#ffffff',
        font: {
          size: 18,
          weight: 'bold',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#666666',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const flow = sortedData[context.dataIndex];
            const value = Math.abs(context.parsed.y);
            const isIncoming = context.datasetIndex === 0;
            
            return [
              `${isIncoming ? 'Incoming' : 'Outgoing'}: ${value.toFixed(4)} ${flow.token.symbol}`,
              `Net: ${flow.net > 0 ? '+' : ''}${flow.net.toFixed(4)} ${flow.token.symbol}`,
              `Transactions: ${flow.transactionCount}`,
              `Token: ${flow.token.name}`,
            ];
          },
        },
      },
    },
    scales: {
      x: {
        ticks: {
          color: '#ffffff',
          maxRotation: 45,
          minRotation: 45,
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      y: {
        ticks: {
          color: '#ffffff',
          callback: function(value: any) {
            return Math.abs(value).toFixed(2);
          },
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
        title: {
          display: true,
          text: 'Token Amount',
          color: '#ffffff',
        },
      },
    },
    elements: {
      bar: {
        borderRadius: 4,
      },
    },
  };

  return (
    <div className="w-full flex justify-center">
      <div 
        className="border border-gray-700 rounded-lg bg-gray-900/50 p-4"
        style={{ width: width, height: height }}
      >
        <Bar data={chartData} options={options} />
      </div>
    </div>
  );
};

export default TokenFlowChart;
