"use client";

import React from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bubble } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
  Legend
);

export interface ProtocolInteraction {
  protocol: string;
  category: string;
  color: string;
  transactionCount: number;
  totalValue?: number;
  description?: string;
}

interface ProtocolBubbleChartProps {
  data: ProtocolInteraction[];
  width?: number;
  height?: number;
}

const ProtocolBubbleChart: React.FC<ProtocolBubbleChartProps> = ({
  data,
  width = 800,
  height = 600,
}) => {
  if (!data.length) {
    return (
      <div className="w-full flex justify-center items-center h-96 border border-gray-700 rounded-lg bg-gray-900/50">
        <p className="text-gray-400">No protocol interactions to display</p>
      </div>
    );
  }

  // Prepare data for Chart.js bubble chart
  const chartData = {
    datasets: data.map((protocol, index) => ({
      label: protocol.protocol,
      data: [
        {
          x: index * 100 + Math.random() * 50, // Spread horizontally
          y: Math.random() * 100 + 50, // Random vertical position
          r: Math.max(protocol.transactionCount * 3, 15), // Bubble size based on transactions
        },
      ],
      backgroundColor: protocol.color + "80", // Add transparency
      borderColor: protocol.color,
      borderWidth: 2,
      hoverBackgroundColor: protocol.color,
      hoverBorderWidth: 3,
    })),
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          color: "#ffffff",
          usePointStyle: true,
          pointStyle: "circle",
        },
      },
      title: {
        display: true,
        text: "DeFi Protocol Interactions",
        color: "#ffffff",
        font: {
          size: 18,
          weight: "bold",
        },
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "#ffffff",
        bodyColor: "#ffffff",
        borderColor: "#666666",
        borderWidth: 1,
        callbacks: {
          label: function (context: any) {
            const protocol = data[context.datasetIndex];
            return [
              `Protocol: ${protocol.protocol}`,
              `Category: ${protocol.category}`,
              `Transactions: ${protocol.transactionCount}`,
              protocol.description ? `${protocol.description}` : "",
            ].filter(Boolean);
          },
        },
      },
    },
    scales: {
      x: {
        display: false,
        min: -50,
        max: data.length * 100 + 50,
      },
      y: {
        display: false,
        min: 0,
        max: 200,
      },
    },
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
    interaction: {
      intersect: false,
      mode: "nearest" as const,
    },
  };

  return (
    <div className="w-full flex justify-center">
      <div
        className="border border-gray-700 rounded-lg bg-gray-900/50 p-4"
        style={{ width: width, height: height }}
      >
        <Bubble data={chartData} options={options} />
      </div>
    </div>
  );
};

export default ProtocolBubbleChart;
