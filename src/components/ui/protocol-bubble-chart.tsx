'use client';

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface ProtocolInteraction {
  protocol: string;
  category: string;
  color: string;
  transactionCount: number;
  totalValue?: number;
  description?: string;
}

interface ProtocolBubbleChartProps {
  data: ProtocolInteraction[];
  width?: number;
  height?: number;
}

const ProtocolBubbleChart: React.FC<ProtocolBubbleChartProps> = ({
  data,
  width = 800,
  height = 600,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!data.length || !svgRef.current) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Create scales
    const maxTransactions = d3.max(data, d => d.transactionCount) || 1;
    const radiusScale = d3.scaleSqrt()
      .domain([1, maxTransactions])
      .range([20, 80]);

    // Prepare data for simulation
    const nodes = data.map(d => ({
      ...d,
      radius: radiusScale(d.transactionCount),
      x: width / 2,
      y: height / 2,
    }));

    // Create force simulation
    const simulation = d3.forceSimulation(nodes)
      .force('charge', d3.forceManyBody().strength(-100))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(d => d.radius + 5));

    // Create container group
    const container = svg.append('g');

    // Create bubbles
    const bubbles = container.selectAll('.bubble')
      .data(nodes)
      .enter()
      .append('g')
      .attr('class', 'bubble')
      .style('cursor', 'pointer');

    // Add circles
    bubbles.append('circle')
      .attr('r', d => d.radius)
      .attr('fill', d => d.color)
      .attr('fill-opacity', 0.7)
      .attr('stroke', d => d.color)
      .attr('stroke-width', 2)
      .on('mouseover', function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .attr('fill-opacity', 0.9)
          .attr('stroke-width', 3);

        // Show tooltip
        const tooltip = d3.select('body').append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '10px')
          .style('border-radius', '5px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition()
          .duration(200)
          .style('opacity', 1);

        tooltip.html(`
          <strong>${d.protocol}</strong><br/>
          Category: ${d.category}<br/>
          Transactions: ${d.transactionCount}<br/>
          ${d.description ? `<em>${d.description}</em>` : ''}
        `)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');
      })
      .on('mouseout', function() {
        d3.select(this)
          .transition()
          .duration(200)
          .attr('fill-opacity', 0.7)
          .attr('stroke-width', 2);

        d3.selectAll('.tooltip').remove();
      });

    // Add text labels
    bubbles.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '0.3em')
      .style('fill', 'white')
      .style('font-size', d => Math.min(d.radius / 3, 14) + 'px')
      .style('font-weight', 'bold')
      .style('pointer-events', 'none')
      .text(d => d.protocol);

    // Add transaction count
    bubbles.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '1.5em')
      .style('fill', 'white')
      .style('font-size', d => Math.min(d.radius / 4, 10) + 'px')
      .style('pointer-events', 'none')
      .text(d => `${d.transactionCount} txs`);

    // Update positions on simulation tick
    simulation.on('tick', () => {
      bubbles.attr('transform', d => `translate(${d.x},${d.y})`);
    });

    // Cleanup
    return () => {
      simulation.stop();
      d3.selectAll('.tooltip').remove();
    };
  }, [data, width, height]);

  return (
    <div className="w-full flex justify-center">
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="border border-gray-700 rounded-lg bg-gray-900/50"
      />
    </div>
  );
};

export default ProtocolBubbleChart;
