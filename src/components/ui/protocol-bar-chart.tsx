'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { ProtocolInteraction } from './protocol-bubble-chart';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ProtocolBarChartProps {
  data: ProtocolInteraction[];
  width?: number;
  height?: number;
}

const ProtocolBarChart: React.FC<ProtocolBarChartProps> = ({
  data,
  width = 800,
  height = 400,
}) => {
  if (!data.length) {
    return (
      <div className="w-full flex justify-center items-center h-96 border border-gray-700 rounded-lg bg-gray-900/50">
        <p className="text-gray-400">No protocol interactions to display</p>
      </div>
    );
  }

  // Sort data by transaction count for better visualization
  const sortedData = [...data].sort((a, b) => b.transactionCount - a.transactionCount);

  const chartData = {
    labels: sortedData.map(protocol => protocol.protocol),
    datasets: [
      {
        label: 'Transaction Count',
        data: sortedData.map(protocol => protocol.transactionCount),
        backgroundColor: sortedData.map(protocol => protocol.color + '80'), // Add transparency
        borderColor: sortedData.map(protocol => protocol.color),
        borderWidth: 2,
        hoverBackgroundColor: sortedData.map(protocol => protocol.color),
        hoverBorderWidth: 3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // Hide legend for cleaner look
      },
      title: {
        display: true,
        text: 'DeFi Protocol Interactions',
        color: '#ffffff',
        font: {
          size: 18,
          weight: 'bold',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#666666',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const protocol = sortedData[context.dataIndex];
            return [
              `Protocol: ${protocol.protocol}`,
              `Category: ${protocol.category}`,
              `Transactions: ${protocol.transactionCount}`,
              protocol.description ? `${protocol.description}` : '',
            ].filter(Boolean);
          },
        },
      },
    },
    scales: {
      x: {
        ticks: {
          color: '#ffffff',
          maxRotation: 45,
          minRotation: 45,
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      y: {
        beginAtZero: true,
        ticks: {
          color: '#ffffff',
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
        title: {
          display: true,
          text: 'Number of Transactions',
          color: '#ffffff',
        },
      },
    },
    elements: {
      bar: {
        borderRadius: 4,
      },
    },
  };

  return (
    <div className="w-full flex justify-center">
      <div 
        className="border border-gray-700 rounded-lg bg-gray-900/50 p-4"
        style={{ width: width, height: height }}
      >
        <Bar data={chartData} options={options} />
      </div>
    </div>
  );
};

export default ProtocolBarChart;
