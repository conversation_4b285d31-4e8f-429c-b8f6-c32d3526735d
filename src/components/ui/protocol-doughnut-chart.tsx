'use client';

import React from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import { ProtocolInteraction } from './protocol-bubble-chart';

ChartJS.register(
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface ProtocolDoughnutChartProps {
  data: ProtocolInteraction[];
  width?: number;
  height?: number;
}

const ProtocolDoughnutChart: React.FC<ProtocolDoughnutChartProps> = ({
  data,
  width = 400,
  height = 400,
}) => {
  if (!data.length) {
    return (
      <div className="w-full flex justify-center items-center h-96 border border-gray-700 rounded-lg bg-gray-900/50">
        <p className="text-gray-400">No protocol interactions to display</p>
      </div>
    );
  }

  // Group by category and sum transactions
  const categoryData = data.reduce((acc, protocol) => {
    if (!acc[protocol.category]) {
      acc[protocol.category] = {
        count: 0,
        protocols: [],
        color: protocol.color,
      };
    }
    acc[protocol.category].count += protocol.transactionCount;
    acc[protocol.category].protocols.push(protocol.protocol);
    return acc;
  }, {} as Record<string, { count: number; protocols: string[]; color: string }>);

  const categories = Object.keys(categoryData);
  const categoryColors = [
    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
  ];

  const chartData = {
    labels: categories,
    datasets: [
      {
        data: categories.map(category => categoryData[category].count),
        backgroundColor: categories.map((_, index) => categoryColors[index % categoryColors.length] + '80'),
        borderColor: categories.map((_, index) => categoryColors[index % categoryColors.length]),
        borderWidth: 2,
        hoverBackgroundColor: categories.map((_, index) => categoryColors[index % categoryColors.length]),
        hoverBorderWidth: 3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          color: '#ffffff',
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 20,
        },
      },
      title: {
        display: true,
        text: 'Protocol Categories',
        color: '#ffffff',
        font: {
          size: 18,
          weight: 'bold',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#666666',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const category = categories[context.dataIndex];
            const categoryInfo = categoryData[category];
            return [
              `Category: ${category}`,
              `Transactions: ${categoryInfo.count}`,
              `Protocols: ${categoryInfo.protocols.join(', ')}`,
            ];
          },
        },
      },
    },
    cutout: '50%', // Makes it a doughnut instead of pie
  };

  return (
    <div className="w-full flex justify-center">
      <div 
        className="border border-gray-700 rounded-lg bg-gray-900/50 p-4"
        style={{ width: width, height: height }}
      >
        <Doughnut data={chartData} options={options} />
      </div>
    </div>
  );
};

export default ProtocolDoughnutChart;
