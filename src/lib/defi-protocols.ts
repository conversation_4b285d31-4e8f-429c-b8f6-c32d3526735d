export interface DeFiProtocol {
  address: string;
  name: string;
  protocol: string;
  category: string;
  color: string;
  description?: string;
}

export const DEFI_PROTOCOLS: Record<string, DeFiProtocol> = {
  // Uniswap
  "0x7a250d5630b4cf539739df2c5dacb4c659f2488d": {
    address: "0x7a250d5630b4cf539739df2c5dacb4c659f2488d",
    name: "Uniswap V2 Router",
    protocol: "Uniswap",
    category: "DEX",
    color: "#FF007A",
    description: "Decentralized exchange"
  },
  "0xe592427a0aece92de3edee1f18e0157c05861564": {
    address: "0xe592427a0aece92de3edee1f18e0157c05861564",
    name: "Uniswap V3 Router",
    protocol: "Uniswap",
    category: "DEX",
    color: "#FF007A"
  },
  
  // Aave
  "0x7d2768de32b0b80b7a3454c06bdac94a69ddc7a9": {
    address: "0x7d2768de32b0b80b7a3454c06bdac94a69ddc7a9",
    name: "Aave Lending Pool",
    protocol: "Aave",
    category: "Lending",
    color: "#B6509E",
    description: "Lending and borrowing protocol"
  },
  "0x398ec7346dcd622edc5ae82352f02be94c62d119": {
    address: "0x398ec7346dcd622edc5ae82352f02be94c62d119",
    name: "Aave V2 Lending Pool",
    protocol: "Aave",
    category: "Lending",
    color: "#B6509E"
  },
  
  // Compound
  "******************************************": {
    address: "******************************************",
    name: "Compound Comptroller",
    protocol: "Compound",
    category: "Lending",
    color: "#00D395",
    description: "Lending protocol"
  },
  
  // Curve
  "******************************************": {
    address: "******************************************",
    name: "Curve Tricrypto Pool",
    protocol: "Curve",
    category: "DEX",
    color: "#40E0D0",
    description: "Stablecoin exchange"
  },
  "******************************************": {
    address: "******************************************",
    name: "Curve 3Pool",
    protocol: "Curve",
    category: "DEX",
    color: "#40E0D0"
  },
  
  // SushiSwap
  "******************************************": {
    address: "******************************************",
    name: "SushiSwap Router",
    protocol: "SushiSwap",
    category: "DEX",
    color: "#FA52A0",
    description: "Decentralized exchange"
  },
  
  // 1inch
  "******************************************": {
    address: "******************************************",
    name: "1inch V4 Router",
    protocol: "1inch",
    category: "Aggregator",
    color: "#1FC7D4",
    description: "DEX aggregator"
  },
  
  // Balancer
  "******************************************": {
    address: "******************************************",
    name: "Balancer Vault",
    protocol: "Balancer",
    category: "DEX",
    color: "#384aff",
    description: "Automated portfolio manager"
  },
  
  // MakerDAO
  "0x35d1b3f3d7966a1dfe207aa4514c12a259a0492b": {
    address: "0x35d1b3f3d7966a1dfe207aa4514c12a259a0492b",
    name: "MakerDAO Vault Manager",
    protocol: "MakerDAO",
    category: "CDP",
    color: "#1AAB9B",
    description: "Collateralized debt positions"
  },
  
  // Yearn Finance
  "0x0bc529c00c6401aef6d220be8c6ea1667f6ad93e": {
    address: "0x0bc529c00c6401aef6d220be8c6ea1667f6ad93e",
    name: "Yearn YFI Vault",
    protocol: "Yearn",
    category: "Yield",
    color: "#0074D9",
    description: "Yield farming protocol"
  },
  
  // Synthetix
  "0xc011a73ee8576fb46f5e1c5751ca3b9fe0af2a6f": {
    address: "0xc011a73ee8576fb46f5e1c5751ca3b9fe0af2a6f",
    name: "Synthetix SNX",
    protocol: "Synthetix",
    category: "Derivatives",
    color: "#00D4FF",
    description: "Synthetic assets protocol"
  },
  
  // Chainlink
  "0x514910771af9ca656af840dff83e8264ecf986ca": {
    address: "0x514910771af9ca656af840dff83e8264ecf986ca",
    name: "Chainlink LINK",
    protocol: "Chainlink",
    category: "Oracle",
    color: "#375BD2",
    description: "Decentralized oracle network"
  },
  
  // OpenSea
  "******************************************": {
    address: "******************************************",
    name: "OpenSea Wyvern Exchange",
    protocol: "OpenSea",
    category: "NFT",
    color: "#2081E2",
    description: "NFT marketplace"
  },
  
  // Lido
  "******************************************": {
    address: "******************************************",
    name: "Lido stETH",
    protocol: "Lido",
    category: "Staking",
    color: "#00A3FF",
    description: "Liquid staking protocol"
  },
  
  // Rocket Pool
  "******************************************": {
    address: "******************************************",
    name: "Rocket Pool rETH",
    protocol: "Rocket Pool",
    category: "Staking",
    color: "#FF6B35",
    description: "Decentralized staking protocol"
  },
  
  // Convex
  "******************************************": {
    address: "******************************************",
    name: "Convex Booster",
    protocol: "Convex",
    category: "Yield",
    color: "#FF6B6B",
    description: "Curve yield farming"
  }
};

export function getProtocolByAddress(address: string): DeFiProtocol | null {
  const normalizedAddress = address.toLowerCase();
  
  for (const [protocolAddress, protocol] of Object.entries(DEFI_PROTOCOLS)) {
    if (protocolAddress.toLowerCase() === normalizedAddress) {
      return protocol;
    }
  }
  
  return null;
}

export function getProtocolCategories(): string[] {
  const categories = new Set<string>();
  Object.values(DEFI_PROTOCOLS).forEach(protocol => {
    categories.add(protocol.category);
  });
  return Array.from(categories);
}

export function getProtocolsByCategory(category: string): DeFiProtocol[] {
  return Object.values(DEFI_PROTOCOLS).filter(protocol => protocol.category === category);
}
