export interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
}

export const TOKEN_MAPPING: Record<string, TokenInfo> = {
  // ETH (native)
  "******************************************": {
    address: "******************************************",
    symbol: "ETH",
    name: "Ethereum",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  },
  
  // USDT
  "******************************************": {
    address: "******************************************",
    symbol: "USDT",
    name: "Tether USD",
    decimals: 6,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  },
  
  // USDC
  "******************************************": {
    address: "******************************************",
    symbol: "USDC",
    name: "USD Coin",
    decimals: 6,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  },
  
  // WETH
  "******************************************": {
    address: "******************************************",
    symbol: "WETH",
    name: "Wrapped Ether",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  },
  
  // UNI
  "******************************************": {
    address: "******************************************",
    symbol: "UNI",
    name: "Uniswap",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  },
  
  // LINK
  "******************************************": {
    address: "******************************************",
    symbol: "LINK",
    name: "ChainLink Token",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  },
  
  // AAVE
  "******************************************": {
    address: "******************************************",
    symbol: "AAVE",
    name: "Aave Token",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  },
  
  // COMP
  "******************************************": {
    address: "******************************************",
    symbol: "COMP",
    name: "Compound",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  },
  
  // SUSHI
  "0x6b3595068778dd592e39a122f4f5a5cf09c90fe2": {
    address: "0x6b3595068778dd592e39a122f4f5a5cf09c90fe2",
    symbol: "SUSHI",
    name: "SushiToken",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0x6b3595068778dd592e39a122f4f5a5cf09c90fe2.png"
  },
  
  // CRV
  "0xd533a949740bb3306d119cc777fa900ba034cd52": {
    address: "0xd533a949740bb3306d119cc777fa900ba034cd52",
    symbol: "CRV",
    name: "Curve DAO Token",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0xd533a949740bb3306d119cc777fa900ba034cd52.png"
  },
  
  // YFI
  "0x0bc529c00c6401aef6d220be8c6ea1667f6ad93e": {
    address: "0x0bc529c00c6401aef6d220be8c6ea1667f6ad93e",
    symbol: "YFI",
    name: "yearn.finance",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0x0bc529c00c6401aef6d220be8c6ea1667f6ad93e.png"
  },
  
  // SNX
  "0xc011a73ee8576fb46f5e1c5751ca3b9fe0af2a6f": {
    address: "0xc011a73ee8576fb46f5e1c5751ca3b9fe0af2a6f",
    symbol: "SNX",
    name: "Synthetix Network Token",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0xc011a73ee8576fb46f5e1c5751ca3b9fe0af2a6f.png"
  },
  
  // MKR
  "0x9f8f72aa9304c8b593d555f12ef6589cc3a579a2": {
    address: "0x9f8f72aa9304c8b593d555f12ef6589cc3a579a2",
    symbol: "MKR",
    name: "Maker",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0x9f8f72aa9304c8b593d555f12ef6589cc3a579a2.png"
  },
  
  // BAT
  "0x0d8775f648430679a709e98d2b0cb6250d2887ef": {
    address: "0x0d8775f648430679a709e98d2b0cb6250d2887ef",
    symbol: "BAT",
    name: "Basic Attention Token",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0x0d8775f648430679a709e98d2b0cb6250d2887ef.png"
  },
  
  // ZRX
  "0xe41d2489571d322189246dafa5ebde1f4699f498": {
    address: "0xe41d2489571d322189246dafa5ebde1f4699f498",
    symbol: "ZRX",
    name: "0x Protocol Token",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0xe41d2489571d322189246dafa5ebde1f4699f498.png"
  },
  
  // ENJ
  "0xf629cbd94d3791c9250152bd8dfbdf380e2a3b9c": {
    address: "0xf629cbd94d3791c9250152bd8dfbdf380e2a3b9c",
    symbol: "ENJ",
    name: "Enjin Coin",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0xf629cbd94d3791c9250152bd8dfbdf380e2a3b9c.png"
  },
  
  // MANA
  "0x0f5d2fb29fb7d3cfee444a200298f468908cc942": {
    address: "0x0f5d2fb29fb7d3cfee444a200298f468908cc942",
    symbol: "MANA",
    name: "Decentraland MANA",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0x0f5d2fb29fb7d3cfee444a200298f468908cc942.png"
  },
  
  // SAND
  "0x3845badade8e6dff049820680d1f14bd3903a5d0": {
    address: "0x3845badade8e6dff049820680d1f14bd3903a5d0",
    symbol: "SAND",
    name: "The Sandbox",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/0x3845badade8e6dff049820680d1f14bd3903a5d0.png"
  },
  
  // AXS
  "******************************************": {
    address: "******************************************",
    symbol: "AXS",
    name: "Axie Infinity Shard",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  },
  
  // SHIB
  "******************************************": {
    address: "******************************************",
    symbol: "SHIB",
    name: "SHIBA INU",
    decimals: 18,
    logoURI: "https://tokens.1inch.io/******************************************.png"
  }
};

export function getTokenInfo(address: string): TokenInfo | null {
  const normalizedAddress = address.toLowerCase();
  
  // Check for ETH (native token)
  if (normalizedAddress === "******************************************" || 
      normalizedAddress === "******************************************") {
    return TOKEN_MAPPING["******************************************"];
  }
  
  // Check for known tokens
  for (const [tokenAddress, tokenInfo] of Object.entries(TOKEN_MAPPING)) {
    if (tokenAddress.toLowerCase() === normalizedAddress) {
      return tokenInfo;
    }
  }
  
  return null;
}

export function formatTokenAmount(amount: string, decimals: number): number {
  const bigIntAmount = BigInt(amount);
  const divisor = BigInt(10 ** decimals);
  const result = Number(bigIntAmount) / Number(divisor);
  return result;
}
