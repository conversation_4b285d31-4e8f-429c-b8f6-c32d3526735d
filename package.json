{"name": "web3-mini-apps", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@21st-extension/react": "^0.5.14", "@21st-extension/toolbar-next": "^0.5.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@tabler/icons-react": "^3.34.0", "@types/d3": "^7.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}